/**
 * Configuration for the API Gateway
 * Environment-specific settings are loaded from .env file
 */

const config = {
  // Service URLs - these would be environment variables in production
  services: {
    auth: {
      url: process.env.AUTH_SERVICE_URL || 'http://localhost:4001'
    },
    booking: {
      url: process.env.BOOKING_SERVICE_URL || 'http://localhost:4002'
    },
    facility: {
      url: process.env.FACILITY_SERVICE_URL || 'http://localhost:4003'
    },
    payment: {
      url: process.env.PAYMENT_SERVICE_URL || 'http://localhost:4004'
    }
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'development-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d'
  },

  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization']
  },

  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env.RATE_LIMIT_MAX || 100 // limit each IP to 100 requests per windowMs
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info'
  }
};

module.exports = config;
