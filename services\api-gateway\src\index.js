require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const proxy = require('express-http-proxy');
const rateLimit = require('express-rate-limit');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const { logger } = require('./utils/logger');
const { verifyToken } = require('./middleware/auth');
const routes = require('./routes');

// Load configuration
const config = require('../config');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Apply middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS support
app.use(express.json()); // Parse JSON bodies
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } })); // Logging

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later'
});
app.use(limiter);

// API documentation
try {
  const path = require('path');
  const swaggerPath = path.resolve(__dirname, '../config/swagger.yaml');
  console.log('Loading Swagger from:', swaggerPath);
  const swaggerDocument = YAML.load(swaggerPath);
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));
} catch (error) {
  logger.warn('Swagger documentation not available:', error.message);
  console.error('Swagger error:', error);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'UP', timestamp: new Date().toISOString() });
});

// API routes
app.use('/api', routes);

// Service proxies
// Auth Service (middleware handles public paths internally)
app.use('/auth', verifyToken, proxy(config.services.auth.url, {
  proxyReqPathResolver: (req) => `/api/auth${req.url}`
}));

// Booking Service
app.use('/bookings', verifyToken, proxy(config.services.booking.url, {
  proxyReqPathResolver: (req) => `/api${req.url}`
}));

// Facility Service
app.use('/facilities', verifyToken, proxy(config.services.facility.url, {
  proxyReqPathResolver: (req) => `/api${req.url}`
}));

// Payment Service
app.use('/payments', verifyToken, proxy(config.services.payment.url, {
  proxyReqPathResolver: (req) => `/api${req.url}`
}));

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : err.message
  });
});

// Start the server
app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`);
});

// For testing purposes
module.exports = app;
