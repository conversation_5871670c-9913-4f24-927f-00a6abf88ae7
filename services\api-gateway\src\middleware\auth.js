const jwt = require('jsonwebtoken');
const config = require('../../config');
const { logger } = require('../utils/logger');

/**
 * Middleware to verify JWT token
 * Excludes public routes that don't require authentication
 */
const verifyToken = (req, res, next) => {
  // List of paths that don't require authentication
  const publicPaths = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/facilities/public'
  ];

  // Check if the path is public
  console.log('Checking path:', req.path, 'Original URL:', req.originalUrl);
  const isPublicPath = publicPaths.some(path => req.originalUrl.startsWith(path));
  console.log('Is public path:', isPublicPath);
  if (isPublicPath) {
    return next();
  }

  // Get token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    logger.warn(`Authentication failed: No token provided for ${req.path}`);
    return res.status(401).json({ error: 'Authentication required' });
  }

  const token = authHeader.split(' ')[1];

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret);

    // Add user info to request
    req.user = decoded;

    next();
  } catch (error) {
    logger.warn(`Authentication failed: Invalid token for ${req.path}`, { error: error.message });
    return res.status(401).json({ error: 'Invalid token' });
  }
};

module.exports = { verifyToken };
